# AlphaEventWatch – Incident Management System

## 🚀 Features
- Incident reporting with categorization and priority
- Real-time WebSocket updates
- Role-based authentication (JWT)
- AdminJS GUI
- PDF report export
- File upload for incidents
- Stand down button with report generator

## 🛠️ Deployment

### Option 1: Windows (Recommended)
Run:


---

## 🧪 Seeding the Database

To populate the database with sample data, run:

```bash
docker-compose exec backend node db/seed.mjs
```

This creates:

- 1 Admin User (username: `admin`, password hash: `changeme`)
- 1 Entity, Category, and Event
- 1 Incident tied to them
- 1 Chat Message
- 1 IncidentLog

import AdminJS from 'adminjs'
import AdminJSExpress from '@adminjs/express'
import * as models from './models/index.js'
import express from 'express'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const admin = new AdminJS({
  rootPath: '/admin',
  branding: {
    companyName: 'Alpha Event Watch',
    logo: '/logo.png',
    softwareBrothers: false,
  },
  resources: [
    { resource: models.User },
    { resource: models.Entity },
    { resource: models.Event },
    { resource: models.Category },
    { resource: models.Incident },
    { resource: models.Message },
    { resource: models.IncidentLog }
  ],
})

const router = AdminJSExpress.buildRouter(admin)

const withStaticLogo = (app) => {
  app.use('/logo.png', express.static(path.join(__dirname, 'public', 'logo.png')))
}

export { admin, router, withStaticLogo }


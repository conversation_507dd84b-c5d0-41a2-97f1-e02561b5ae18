import { sequelize, User, Event, Entity, Category, Incident, Message, IncidentLog } from '../models/index.js';

const runSeed = async () => {
  try {
    await sequelize.sync({ force: true });

    const entity = await Entity.create({ name: 'Main HQ', location: 'Central Station' });
    const category = await Category.create({ name: 'Security' });
    const event = await Event.create({ name: 'Emergency Drill', description: 'Simulated lockdown', date: new Date() });

    const user = await User.create({
      username: 'admin',
      password_hash: 'changeme',
      role: 'admin',
      entity_id: entity.id,
    });

    const incident = await Incident.create({
      title: 'Unauthorized Access',
      description: 'Suspicious activity detected at Gate B.',
      status: 'open',
      entity_id: entity.id,
      category_id: category.id,
      event_id: event.id,
      created_by: user.id,
    });

    await Message.create({
      incident_id: incident.id,
      user_id: user.id,
      content: 'Initial response unit dispatched.'
    });

    await IncidentLog.create({
      incident_id: incident.id,
      action: 'Reported',
      performed_by: user.id,
    });

    console.log('✅ Seed completed');
  } catch (error) {
    console.error('❌ Seed failed', error);
  } finally {
    await sequelize.close();
  }
};

runSeed();

import express from 'express';
import http from 'http';
import cors from 'cors';
import { Server } from 'socket.io';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

import db from './models/index.js';
import { admin, router as adminRouter, withStaticLogo } from './admin.mjs';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = http.createServer(app);

const io = new Server(server, {
  cors: {
    origin: 'https://alphaeventwatch.com',
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
  },
});

const PORT = process.env.PORT || 4000;

// Middlewares
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve custom AdminJS logo and static assets
withStatic<PERSON>ogo(app);

// AdminJS panel
app.use(admin.options.rootPath, adminRouter);

// API Routes
import incidentsRouter from './routes/incidents.js';
import usersRouter from './routes/users.js';
import eventsRouter from './routes/events.js';
import categoriesRouter from './routes/categories.js';
import entitiesRouter from './routes/entities.js';
import authRouter from './routes/auth.js';
import messagesRouter from './routes/messages.js';

app.use('/api/incidents', incidentsRouter);
app.use('/api/users', usersRouter);
app.use('/api/events', eventsRouter);
app.use('/api/categories', categoriesRouter);
app.use('/api/entities', entitiesRouter);
app.use('/api/auth', authRouter);
app.use('/api/messages', messagesRouter);

// WebSocket Events
io.on('connection', (socket) => {
  console.log(`📡 Client connected: ${socket.id}`);

  socket.on('incidentCreated', (data) => {
    socket.broadcast.emit('newIncident', data);
  });

  socket.on('statusChanged', (data) => {
    socket.broadcast.emit('incidentUpdated', data);
  });

  socket.on('sendMessage', (message) => {
    socket.broadcast.emit('receiveMessage', message);
  });

  socket.on('disconnect', () => {
    console.log(`🔌 Client disconnected: ${socket.id}`);
  });
});

// Start Server
server.listen(PORT, async () => {
  try {
    await db.sequelize.sync();
    console.log(`✅ Backend running at http://localhost:${PORT}`);
    console.log(`🔐 Admin Panel at ${admin.options.rootPath}`);
  } catch (err) {
    console.error('❌ Failed to connect to database:', err);
  }
});

const Category = (sequelize, DataTypes) => {
  const Category = sequelize.define('Category', {
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    }
  });

  Category.associate = models => {
    Category.hasMany(models.Incident, { foreignKey: 'category_id' });
  };

  return Category;
};

export default Category;

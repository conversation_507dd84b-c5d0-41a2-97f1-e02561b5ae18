const Entity = (sequelize, DataTypes) => {
  const Entity = sequelize.define('Entity', {
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    location: {
      type: DataTypes.STRING,
      allowNull: true,
    }
  });

  Entity.associate = models => {
    Entity.hasMany(models.User, { foreignKey: 'entity_id' });
    Entity.hasMany(models.Incident, { foreignKey: 'entity_id' });
  };

  return Entity;
};

export default Entity;

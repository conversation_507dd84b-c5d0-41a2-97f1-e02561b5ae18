const IncidentLog = (sequelize, DataTypes) => {
  const IncidentLog = sequelize.define('IncidentLog', {
    incident_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    action: {
      type: DataTypes.STRING,
      allowNull: false
    },
    details: {
      type: DataTypes.JSONB
    }
  });

  return IncidentLog;
};

export default IncidentLog;

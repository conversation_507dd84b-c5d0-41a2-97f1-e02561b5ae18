const Incident = (sequelize, DataTypes) => {
  const Incident = sequelize.define('Incident', {
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    priority: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    location: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM('Reported', 'Acknowledged', 'In Progress', 'Resolved', 'Closed'),
      defaultValue: 'Reported',
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    assigned_to: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    event_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    }
  });

  Incident.associate = models => {
    Incident.belongsTo(models.User, { foreignKey: 'created_by', as: 'creator' });
    Incident.belongsTo(models.User, { foreignKey: 'assigned_to', as: 'assignee' });
    Incident.belongsTo(models.Category, { foreignKey: 'category_id' });
    Incident.belongsTo(models.Event, { foreignKey: 'event_id' });
  };

  return Incident;
};

export default Incident;

import Sequelize from 'sequelize';
import sequelize from '../config/database.js';

import Entity from './entity.js';
import Category from './category.js';
import Event from './event.js';
import IncidentLog from './incidentlogs.js';
import Message from './message.js';
import User from './user.js';

Entity.initModel(sequelize);
Category.initModel(sequelize);
Event.initModel(sequelize);
IncidentLog.initModel(sequelize);
Message.initModel(sequelize);
User.initModel(sequelize);

const db = {
  Entity,
  Category,
  Event,
  IncidentLog,
  Message,
  User,
  sequelize,
  Sequelize,
};

export {
  Entity,
  Category,
  Event,
  IncidentLog,
  Message,
  User,
  sequelize,
  Sequelize,
};

export default db;

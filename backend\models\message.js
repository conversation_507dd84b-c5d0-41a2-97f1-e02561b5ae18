const Message = (sequelize, DataTypes) => {
  const Message = sequelize.define('Message', {
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    from_user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    to_entity_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    }
  });

  Message.associate = models => {
    Message.belongsTo(models.User, { foreignKey: 'from_user_id' });
    Message.belongsTo(models.Entity, { foreignKey: 'to_entity_id' });
  };

  return Message;
};

export default Message;

const User = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    username: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    password_hash: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    role: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    entity_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    }
  });

  User.associate = models => {
    User.belongsTo(models.Entity, { foreignKey: 'entity_id' });
    User.hasMany(models.Incident, { foreignKey: 'created_by' });
    User.hasMany(models.Incident, { foreignKey: 'assigned_to' });
  };

  return User;
};

export default User;

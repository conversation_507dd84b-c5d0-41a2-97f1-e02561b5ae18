{"name": "alphaeventwatch-backend", "version": "1.0.0", "type": "module", "main": "index.mjs", "scripts": {"start": "node index.mjs", "dev": "nodemon index.mjs"}, "dependencies": {"adminjs": "^6.8.0", "@adminjs/express": "^4.0.1", "bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.4", "jsonwebtoken": "^9.0.1", "pg": "^8.11.1", "pg-hstore": "^2.3.4", "sequelize": "^6.32.1", "socket.io": "^4.7.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}, "license": "MIT"}
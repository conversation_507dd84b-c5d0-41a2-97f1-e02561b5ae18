const nodemailer = require('nodemailer');
require('dotenv').config();

async function sendEventReportEmail(to, subject, message, attachmentPath) {
  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: Number(process.env.SMTP_PORT),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });

  const mailOptions = {
    from: `"AlphaEventWatch" <${process.env.SMTP_USER}>`,
    to,
    subject,
    text: message,
    attachments: [
      {
        filename: 'event-report.pdf',
        path: attachmentPath,
      },
    ],
  };

  return transporter.sendMail(mailOptions);
}

module.exports = { sendEventReportEmail };

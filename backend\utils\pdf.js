const PDFDocument = require('pdfkit');
const fs = require('fs');

function generateEventReport(event, incidents, filepath) {
  return new Promise((resolve, reject) => {
    try {
      const doc = new PDFDocument();
      const stream = fs.createWriteStream(filepath);
      doc.pipe(stream);

      doc.fontSize(20).text(`Event Report: ${event.name}`, { align: 'center' });
      doc.moveDown().fontSize(12).text(`Date: ${new Date().toLocaleString()}`);
      doc.moveDown().text(`Status: ${event.is_active ? 'Active' : 'Closed'}`);
      doc.moveDown();

      doc.fontSize(14).text('Incidents:', { underline: true });
      incidents.forEach((incident, idx) => {
        doc.moveDown().fontSize(12).text(`${idx + 1}. ${incident.title}`);
        doc.text(`   Category: ${incident.category_id}`);
        doc.text(`   Priority: ${incident.priority}`);
        doc.text(`   Status: ${incident.status}`);
        doc.text(`   Location: ${incident.location}`);
        doc.text(`   Time: ${incident.createdAt}`);
      });

      doc.end();
      stream.on('finish', () => resolve(filepath));
    } catch (err) {
      reject(err);
    }
  });
}

module.exports = { generateEventReport };

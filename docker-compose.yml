services:
  backend:
    build:
      context: ./backend
    volumes:
      - ./backend:/app
    ports:
      - "5000:5000"
    depends_on:
      - db
    command: npm run dev

  frontend:
    build:
      context: ./frontend
    volumes:
      - ./frontend:/app
    ports:
      - "3000:3000"
    depends_on:
      - backend

  db:
    image: postgres:14
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: alphaeventwatch
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  pgdata:

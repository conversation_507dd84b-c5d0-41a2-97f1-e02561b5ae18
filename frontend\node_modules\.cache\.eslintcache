[{"/app/src/index.js": "1", "/app/src/App.jsx": "2", "/app/src/components/ChatPanel.jsx": "3", "/app/src/context/AuthContext.js": "4", "/app/src/components/Sidebar.jsx": "5", "/app/src/components/Dashboard.jsx": "6", "/app/src/components/NewIncidentForm.jsx": "7", "/app/src/pages/LoginPage.jsx": "8", "/app/src/pages/AdminPanel.jsx": "9"}, {"size": 189, "mtime": 1751470707343, "results": "10", "hashOfConfig": "11"}, {"size": 1057, "mtime": 1751470707332, "results": "12", "hashOfConfig": "11"}, {"size": 1144, "mtime": 1751470707363, "results": "13", "hashOfConfig": "11"}, {"size": 607, "mtime": 1751470707401, "results": "14", "hashOfConfig": "11"}, {"size": 562, "mtime": 1751470707378, "results": "15", "hashOfConfig": "11"}, {"size": 1424, "mtime": 1751470707363, "results": "16", "hashOfConfig": "11"}, {"size": 1612, "mtime": 1751470707378, "results": "17", "hashOfConfig": "11"}, {"size": 1281, "mtime": 1751470707435, "results": "18", "hashOfConfig": "11"}, {"size": 413, "mtime": 1751470707425, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "uocqyl", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/app/src/index.js", [], [], "/app/src/App.jsx", [], [], "/app/src/components/ChatPanel.jsx", [], [], "/app/src/context/AuthContext.js", [], [], "/app/src/components/Sidebar.jsx", [], [], "/app/src/components/Dashboard.jsx", [], [], "/app/src/components/NewIncidentForm.jsx", [], [], "/app/src/pages/LoginPage.jsx", [], [], "/app/src/pages/AdminPanel.jsx", [], []]
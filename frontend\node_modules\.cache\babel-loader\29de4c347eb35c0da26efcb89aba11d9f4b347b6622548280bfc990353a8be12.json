{"ast": null, "code": "var _jsxFileName = \"/app/src/pages/LoginPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LoginPage() {\n  _s();\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const res = await axios.post('http://localhost:4000/api/auth/login', {\n        username,\n        password\n      });\n      login(res.data.token);\n      navigate('/dashboard');\n    } catch (err) {\n      alert('Login failed. Check credentials.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 max-w-sm mx-auto bg-white rounded shadow\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-bold mb-4\",\n      children: \"Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        className: \"w-full p-2 mb-2 border\",\n        placeholder: \"Username\",\n        onChange: e => setUsername(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        className: \"w-full p-2 mb-4 border\",\n        type: \"password\",\n        placeholder: \"Password\",\n        onChange: e => setPassword(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"w-full bg-blue-600 text-white py-2\",\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n}\n_s(LoginPage, \"EcTN7OHcCinc7FynmoeYRP2dRi4=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "username", "setUsername", "password", "setPassword", "login", "navigate", "handleSubmit", "e", "preventDefault", "res", "post", "data", "token", "err", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "placeholder", "onChange", "target", "value", "type", "_c", "$RefreshReg$"], "sources": ["/app/src/pages/LoginPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useAuth } from '../context/AuthContext';\r\n\r\nfunction LoginPage() {\r\n  const [username, setUsername] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const { login } = useAuth();\r\n  const navigate = useNavigate();\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      const res = await axios.post('http://localhost:4000/api/auth/login', { username, password });\r\n      login(res.data.token);\r\n      navigate('/dashboard');\r\n    } catch (err) {\r\n      alert('Login failed. Check credentials.');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-6 max-w-sm mx-auto bg-white rounded shadow\">\r\n      <h2 className=\"text-xl font-bold mb-4\">Login</h2>\r\n      <form onSubmit={handleSubmit}>\r\n        <input className=\"w-full p-2 mb-2 border\" placeholder=\"Username\" onChange={(e) => setUsername(e.target.value)} />\r\n        <input className=\"w-full p-2 mb-4 border\" type=\"password\" placeholder=\"Password\" onChange={(e) => setPassword(e.target.value)} />\r\n        <button type=\"submit\" className=\"w-full bg-blue-600 text-white py-2\">Login</button>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default LoginPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM;IAAEY;EAAM,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC3B,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,GAAG,GAAG,MAAMhB,KAAK,CAACiB,IAAI,CAAC,sCAAsC,EAAE;QAAEV,QAAQ;QAAEE;MAAS,CAAC,CAAC;MAC5FE,KAAK,CAACK,GAAG,CAACE,IAAI,CAACC,KAAK,CAAC;MACrBP,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZC,KAAK,CAAC,kCAAkC,CAAC;IAC3C;EACF,CAAC;EAED,oBACEjB,OAAA;IAAKkB,SAAS,EAAC,8CAA8C;IAAAC,QAAA,gBAC3DnB,OAAA;MAAIkB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EAAC;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACjDvB,OAAA;MAAMwB,QAAQ,EAAEf,YAAa;MAAAU,QAAA,gBAC3BnB,OAAA;QAAOkB,SAAS,EAAC,wBAAwB;QAACO,WAAW,EAAC,UAAU;QAACC,QAAQ,EAAGhB,CAAC,IAAKN,WAAW,CAACM,CAAC,CAACiB,MAAM,CAACC,KAAK;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjHvB,OAAA;QAAOkB,SAAS,EAAC,wBAAwB;QAACW,IAAI,EAAC,UAAU;QAACJ,WAAW,EAAC,UAAU;QAACC,QAAQ,EAAGhB,CAAC,IAAKJ,WAAW,CAACI,CAAC,CAACiB,MAAM,CAACC,KAAK;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjIvB,OAAA;QAAQ6B,IAAI,EAAC,QAAQ;QAACX,SAAS,EAAC,oCAAoC;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACrB,EAAA,CA3BQD,SAAS;EAAA,QAGEH,OAAO,EACRD,WAAW;AAAA;AAAAiC,EAAA,GAJrB7B,SAAS;AA6BlB,eAAeA,SAAS;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
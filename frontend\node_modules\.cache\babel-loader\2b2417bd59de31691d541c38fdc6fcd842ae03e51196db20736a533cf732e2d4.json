{"ast": null, "code": "var _jsxFileName = \"/app/src/components/ChatPanel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport io from 'socket.io-client';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst socket = io('http://localhost:4000');\nfunction ChatPanel() {\n  _s();\n  const {\n    token\n  } = useAuth();\n  const [msg, setMsg] = useState('');\n  const [chat, setChat] = useState([]);\n  useEffect(() => {\n    socket.on('receiveMessage', message => {\n      setChat(prev => [...prev, message]);\n    });\n    return () => socket.off('receiveMessage');\n  }, []);\n  const sendMessage = () => {\n    socket.emit('sendMessage', msg);\n    setChat(prev => [...prev, `You: ${msg}`]);\n    setMsg('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pl-64 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-bold mb-2\",\n      children: \"Team Chat\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border h-64 overflow-y-scroll mb-2 p-2 bg-gray-100\",\n      children: chat.map((m, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        children: m\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 29\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      className: \"border p-2 w-2/3\",\n      value: msg,\n      onChange: e => setMsg(e.target.value)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"bg-blue-600 text-white p-2 ml-2\",\n      onClick: sendMessage,\n      children: \"Send\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n}\n_s(ChatPanel, \"pO2adLe/S6A77PQp/agbbkT2wok=\", false, function () {\n  return [useAuth];\n});\n_c = ChatPanel;\nexport default ChatPanel;\nvar _c;\n$RefreshReg$(_c, \"ChatPanel\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "io", "useAuth", "jsxDEV", "_jsxDEV", "socket", "ChatPanel", "_s", "token", "msg", "setMsg", "chat", "setChat", "on", "message", "prev", "off", "sendMessage", "emit", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "m", "i", "value", "onChange", "e", "target", "onClick", "_c", "$RefreshReg$"], "sources": ["/app/src/components/ChatPanel.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport io from 'socket.io-client';\r\nimport { useAuth } from '../context/AuthContext';\r\n\r\nconst socket = io('http://localhost:4000');\r\n\r\nfunction ChatPanel() {\r\n  const { token } = useAuth();\r\n  const [msg, setMsg] = useState('');\r\n  const [chat, setChat] = useState([]);\r\n\r\n  useEffect(() => {\r\n    socket.on('receiveMessage', message => {\r\n      setChat(prev => [...prev, message]);\r\n    });\r\n\r\n    return () => socket.off('receiveMessage');\r\n  }, []);\r\n\r\n  const sendMessage = () => {\r\n    socket.emit('sendMessage', msg);\r\n    setChat(prev => [...prev, `You: ${msg}`]);\r\n    setMsg('');\r\n  };\r\n\r\n  return (\r\n    <div className=\"pl-64 p-6\">\r\n      <h2 className=\"text-xl font-bold mb-2\">Team Chat</h2>\r\n      <div className=\"border h-64 overflow-y-scroll mb-2 p-2 bg-gray-100\">\r\n        {chat.map((m, i) => <div key={i}>{m}</div>)}\r\n      </div>\r\n      <input className=\"border p-2 w-2/3\" value={msg} onChange={e => setMsg(e.target.value)} />\r\n      <button className=\"bg-blue-600 text-white p-2 ml-2\" onClick={sendMessage}>Send</button>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatPanel;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,MAAM,GAAGJ,EAAE,CAAC,uBAAuB,CAAC;AAE1C,SAASK,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAM;IAAEC;EAAM,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC3B,MAAM,CAACO,GAAG,EAAEC,MAAM,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACW,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAEpCD,SAAS,CAAC,MAAM;IACdM,MAAM,CAACQ,EAAE,CAAC,gBAAgB,EAAEC,OAAO,IAAI;MACrCF,OAAO,CAACG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,OAAO,CAAC,CAAC;IACrC,CAAC,CAAC;IAEF,OAAO,MAAMT,MAAM,CAACW,GAAG,CAAC,gBAAgB,CAAC;EAC3C,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBZ,MAAM,CAACa,IAAI,CAAC,aAAa,EAAET,GAAG,CAAC;IAC/BG,OAAO,CAACG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,QAAQN,GAAG,EAAE,CAAC,CAAC;IACzCC,MAAM,CAAC,EAAE,CAAC;EACZ,CAAC;EAED,oBACEN,OAAA;IAAKe,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBhB,OAAA;MAAIe,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrDpB,OAAA;MAAKe,SAAS,EAAC,oDAAoD;MAAAC,QAAA,EAChET,IAAI,CAACc,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAAKvB,OAAA;QAAAgB,QAAA,EAAcM;MAAC,GAALC,CAAC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eACNpB,OAAA;MAAOe,SAAS,EAAC,kBAAkB;MAACS,KAAK,EAAEnB,GAAI;MAACoB,QAAQ,EAAEC,CAAC,IAAIpB,MAAM,CAACoB,CAAC,CAACC,MAAM,CAACH,KAAK;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzFpB,OAAA;MAAQe,SAAS,EAAC,iCAAiC;MAACa,OAAO,EAAEf,WAAY;MAAAG,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpF,CAAC;AAEV;AAACjB,EAAA,CA7BQD,SAAS;EAAA,QACEJ,OAAO;AAAA;AAAA+B,EAAA,GADlB3B,SAAS;AA+BlB,eAAeA,SAAS;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"/app/src/context/AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { createContext, useState, useContext } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [token, setToken] = useState(localStorage.getItem('token') || '');\n  const login = jwt => {\n    setToken(jwt);\n    localStorage.setItem('token', jwt);\n  };\n  const logout = () => {\n    setToken('');\n    localStorage.removeItem('token');\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: {\n      token,\n      login,\n      logout\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthProvider, \"QVnf2tvMpRt6ts3Y1zPSzbV9SCM=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  return useContext(AuthContext);\n};\n_s2(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["createContext", "useState", "useContext", "jsxDEV", "_jsxDEV", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "token", "setToken", "localStorage", "getItem", "login", "jwt", "setItem", "logout", "removeItem", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "$RefreshReg$"], "sources": ["/app/src/context/AuthContext.js"], "sourcesContent": ["import { createContext, useState, useContext } from 'react';\r\n\r\nconst AuthContext = createContext();\r\n\r\nexport const AuthProvider = ({ children }) => {\r\n  const [token, setToken] = useState(localStorage.getItem('token') || '');\r\n\r\n  const login = (jwt) => {\r\n    setToken(jwt);\r\n    localStorage.setItem('token', jwt);\r\n  };\r\n\r\n  const logout = () => {\r\n    setToken('');\r\n    localStorage.removeItem('token');\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={{ token, login, logout }}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useAuth = () => useContext(AuthContext);\r\n"], "mappings": ";;;AAAA,SAASA,aAAa,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,WAAW,gBAAGL,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMM,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAACU,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;EAEvE,MAAMC,KAAK,GAAIC,GAAG,IAAK;IACrBJ,QAAQ,CAACI,GAAG,CAAC;IACbH,YAAY,CAACI,OAAO,CAAC,OAAO,EAAED,GAAG,CAAC;EACpC,CAAC;EAED,MAAME,MAAM,GAAGA,CAAA,KAAM;IACnBN,QAAQ,CAAC,EAAE,CAAC;IACZC,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;EAClC,CAAC;EAED,oBACEb,OAAA,CAACC,WAAW,CAACa,QAAQ;IAACC,KAAK,EAAE;MAAEV,KAAK;MAAEI,KAAK;MAAEG;IAAO,CAAE;IAAAT,QAAA,EACnDA;EAAQ;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACf,EAAA,CAlBWF,YAAY;AAAAkB,EAAA,GAAZlB,YAAY;AAoBzB,OAAO,MAAMmB,OAAO,GAAGA,CAAA;EAAAC,GAAA;EAAA,OAAMxB,UAAU,CAACG,WAAW,CAAC;AAAA;AAACqB,GAAA,CAAxCD,OAAO;AAAA,IAAAD,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
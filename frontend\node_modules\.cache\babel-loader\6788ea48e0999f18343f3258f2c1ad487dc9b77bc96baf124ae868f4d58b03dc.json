{"ast": null, "code": "var _jsxFileName = \"/app/src/components/Dashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport Sidebar from './Sidebar';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Dashboard() {\n  _s();\n  const [incidents, setIncidents] = useState([]);\n  const {\n    token\n  } = useAuth();\n  useEffect(() => {\n    axios.get('http://localhost:4000/api/incidents', {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    }).then(res => setIncidents(res.data)).catch(err => console.error(err));\n  }, [token]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pl-64 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-2xl font-bold mb-4\",\n      children: \"Incident Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"w-full table-auto border-collapse border\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"border p-2\",\n            children: \"Title\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"border p-2\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"border p-2\",\n            children: \"Priority\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"border p-2\",\n            children: \"Location\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: incidents.map(inc => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"border p-2\",\n            children: inc.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"border p-2\",\n            children: inc.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"border p-2\",\n            children: inc.priority\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            className: \"border p-2\",\n            children: inc.location\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }, this)]\n        }, inc.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_s(Dashboard, \"Utq0tNFLjGwj8+B3mSGrQH2wNzw=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "Sidebar", "useAuth", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "incidents", "setIncidents", "token", "get", "headers", "Authorization", "then", "res", "data", "catch", "err", "console", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "inc", "title", "status", "priority", "location", "id", "_c", "$RefreshReg$"], "sources": ["/app/src/components/Dashboard.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport axios from 'axios';\r\nimport Sidebar from './Sidebar';\r\nimport { useAuth } from '../context/AuthContext';\r\n\r\nfunction Dashboard() {\r\n  const [incidents, setIncidents] = useState([]);\r\n  const { token } = useAuth();\r\n\r\n  useEffect(() => {\r\n    axios.get('http://localhost:4000/api/incidents', {\r\n      headers: { Authorization: `Bearer ${token}` }\r\n    })\r\n    .then(res => setIncidents(res.data))\r\n    .catch(err => console.error(err));\r\n  }, [token]);\r\n\r\n  return (\r\n    <div className=\"pl-64 p-6\">\r\n      <h1 className=\"text-2xl font-bold mb-4\">Incident Dashboard</h1>\r\n      <table className=\"w-full table-auto border-collapse border\">\r\n        <thead>\r\n          <tr>\r\n            <th className=\"border p-2\">Title</th>\r\n            <th className=\"border p-2\">Status</th>\r\n            <th className=\"border p-2\">Priority</th>\r\n            <th className=\"border p-2\">Location</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {incidents.map((inc) => (\r\n            <tr key={inc.id}>\r\n              <td className=\"border p-2\">{inc.title}</td>\r\n              <td className=\"border p-2\">{inc.status}</td>\r\n              <td className=\"border p-2\">{inc.priority}</td>\r\n              <td className=\"border p-2\">{inc.location}</td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Dashboard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM;IAAEU;EAAM,CAAC,GAAGP,OAAO,CAAC,CAAC;EAE3BJ,SAAS,CAAC,MAAM;IACdE,KAAK,CAACU,GAAG,CAAC,qCAAqC,EAAE;MAC/CC,OAAO,EAAE;QAAEC,aAAa,EAAE,UAAUH,KAAK;MAAG;IAC9C,CAAC,CAAC,CACDI,IAAI,CAACC,GAAG,IAAIN,YAAY,CAACM,GAAG,CAACC,IAAI,CAAC,CAAC,CACnCC,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC;EACnC,CAAC,EAAE,CAACR,KAAK,CAAC,CAAC;EAEX,oBACEL,OAAA;IAAKgB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBjB,OAAA;MAAIgB,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/DrB,OAAA;MAAOgB,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACzDjB,OAAA;QAAAiB,QAAA,eACEjB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YAAIgB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrCrB,OAAA;YAAIgB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtCrB,OAAA;YAAIgB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxCrB,OAAA;YAAIgB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRrB,OAAA;QAAAiB,QAAA,EACGd,SAAS,CAACmB,GAAG,CAAEC,GAAG,iBACjBvB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YAAIgB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEM,GAAG,CAACC;UAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3CrB,OAAA;YAAIgB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEM,GAAG,CAACE;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5CrB,OAAA;YAAIgB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEM,GAAG,CAACG;UAAQ;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9CrB,OAAA;YAAIgB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEM,GAAG,CAACI;UAAQ;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA,GAJvCE,GAAG,CAACK,EAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKX,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAACnB,EAAA,CArCQD,SAAS;EAAA,QAEEH,OAAO;AAAA;AAAA+B,EAAA,GAFlB5B,SAAS;AAuClB,eAAeA,SAAS;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport { SocketWithoutUpgrade, SocketWithUpgrade } from \"./socket.js\";\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./globals.node.js\";\nexport { Fetch } from \"./transports/polling-fetch.js\";\nexport { XHR as NodeXHR } from \"./transports/polling-xhr.node.js\";\nexport { XHR } from \"./transports/polling-xhr.js\";\nexport { WS as NodeWebSocket } from \"./transports/websocket.node.js\";\nexport { WS as WebSocket } from \"./transports/websocket.js\";\nexport { WT as WebTransport } from \"./transports/webtransport.js\";", "map": {"version": 3, "names": ["Socket", "SocketWithoutUpgrade", "SocketWithUpgrade", "protocol", "Transport", "TransportError", "transports", "installTimerFunctions", "parse", "nextTick", "<PERSON>tch", "XHR", "NodeXHR", "WS", "NodeWebSocket", "WebSocket", "WT", "WebTransport"], "sources": ["/app/node_modules/engine.io-client/build/esm/index.js"], "sourcesContent": ["import { Socket } from \"./socket.js\";\nexport { Socket };\nexport { SocketWithoutUpgrade, SocketWithUpgrade, } from \"./socket.js\";\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./globals.node.js\";\nexport { Fetch } from \"./transports/polling-fetch.js\";\nexport { XHR as NodeXHR } from \"./transports/polling-xhr.node.js\";\nexport { XHR } from \"./transports/polling-xhr.js\";\nexport { WS as NodeWebSocket } from \"./transports/websocket.node.js\";\nexport { WS as WebSocket } from \"./transports/websocket.js\";\nexport { WT as WebTransport } from \"./transports/webtransport.js\";\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASA,MAAM;AACf,SAASC,oBAAoB,EAAEC,iBAAiB,QAAS,aAAa;AACtE,OAAO,MAAMC,QAAQ,GAAGH,MAAM,CAACG,QAAQ;AACvC,SAASC,SAAS,EAAEC,cAAc,QAAQ,gBAAgB;AAC1D,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,qBAAqB,QAAQ,WAAW;AACjD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,KAAK,QAAQ,+BAA+B;AACrD,SAASC,GAAG,IAAIC,OAAO,QAAQ,kCAAkC;AACjE,SAASD,GAAG,QAAQ,6BAA6B;AACjD,SAASE,EAAE,IAAIC,aAAa,QAAQ,gCAAgC;AACpE,SAASD,EAAE,IAAIE,SAAS,QAAQ,2BAA2B;AAC3D,SAASC,EAAE,IAAIC,YAAY,QAAQ,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
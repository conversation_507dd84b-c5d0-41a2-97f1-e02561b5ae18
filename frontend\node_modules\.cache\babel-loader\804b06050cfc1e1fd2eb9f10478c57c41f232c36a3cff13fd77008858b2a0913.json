{"ast": null, "code": "var _jsxFileName = \"/app/src/components/Sidebar.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Sidebar() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-800 text-white w-60 h-full fixed top-0 left-0 p-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-lg font-bold mb-4\",\n      children: \"AlphaEventWatch\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/dashboard\",\n          className: \"block py-2\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/new\",\n          className: \"block py-2\",\n          children: \"New Incident\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/admin\",\n          className: \"block py-2\",\n          children: \"Admin Panel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Sidebar", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["/app/src/components/Sidebar.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { Link } from 'react-router-dom';\r\n\r\nfunction Sidebar() {\r\n  return (\r\n    <div className=\"bg-gray-800 text-white w-60 h-full fixed top-0 left-0 p-5\">\r\n      <h2 className=\"text-lg font-bold mb-4\">AlphaEventWatch</h2>\r\n      <ul>\r\n        <li><Link to=\"/dashboard\" className=\"block py-2\">Dashboard</Link></li>\r\n        <li><Link to=\"/new\" className=\"block py-2\">New Incident</Link></li>\r\n        <li><Link to=\"/admin\" className=\"block py-2\">Admin Panel</Link></li>\r\n      </ul>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Sidebar;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,OAAOA,CAAA,EAAG;EACjB,oBACED,OAAA;IAAKE,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBACxEH,OAAA;MAAIE,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3DP,OAAA;MAAAG,QAAA,gBACEH,OAAA;QAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,YAAY;UAACN,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtEP,OAAA;QAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,MAAM;UAACN,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnEP,OAAA;QAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,QAAQ;UAACN,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEV;AAACE,EAAA,GAXQR,OAAO;AAahB,eAAeA,OAAO;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
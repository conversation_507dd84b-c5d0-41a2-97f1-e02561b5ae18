{"ast": null, "code": "var _jsxFileName = \"/app/src/pages/AdminPanel.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AdminPanel() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pl-64 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-bold mb-4\",\n      children: \"Admin Control Panel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Manage events, users, roles, categories from backend GUI at \", /*#__PURE__*/_jsxDEV(\"code\", {\n        children: \"/admin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 70\n      }, this), \".\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Event control, report exports, and stand-down commands go here (coming upgrade).\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = AdminPanel;\nexport default AdminPanel;\nvar _c;\n$RefreshReg$(_c, \"AdminPanel\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "AdminPanel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/app/src/pages/AdminPanel.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nfunction AdminPanel() {\r\n  return (\r\n    <div className=\"pl-64 p-6\">\r\n      <h2 className=\"text-2xl font-bold mb-4\">Admin Control Panel</h2>\r\n      <p>Manage events, users, roles, categories from backend GUI at <code>/admin</code>.</p>\r\n      <p>Event control, report exports, and stand-down commands go here (coming upgrade).</p>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AdminPanel;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,UAAUA,CAAA,EAAG;EACpB,oBACED,OAAA;IAAKE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBH,OAAA;MAAIE,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChEP,OAAA;MAAAG,QAAA,GAAG,8DAA4D,eAAAH,OAAA;QAAAG,QAAA,EAAM;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,KAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACvFP,OAAA;MAAAG,QAAA,EAAG;IAAgF;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpF,CAAC;AAEV;AAACC,EAAA,GARQP,UAAU;AAUnB,eAAeA,UAAU;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
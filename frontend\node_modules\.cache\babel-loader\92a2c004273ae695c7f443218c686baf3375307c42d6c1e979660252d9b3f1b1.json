{"ast": null, "code": "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n  if (typeof uri === \"object\") {\n    opts = uri;\n    uri = undefined;\n  }\n  opts = opts || {};\n  const parsed = url(uri, opts.path || \"/socket.io\");\n  const source = parsed.source;\n  const id = parsed.id;\n  const path = parsed.path;\n  const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n  const newConnection = opts.forceNew || opts[\"force new connection\"] || false === opts.multiplex || sameNamespace;\n  let io;\n  if (newConnection) {\n    io = new Manager(source, opts);\n  } else {\n    if (!cache[id]) {\n      cache[id] = new Manager(source, opts);\n    }\n    io = cache[id];\n  }\n  if (parsed.query && !opts.query) {\n    opts.query = parsed.queryKey;\n  }\n  return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n  Manager,\n  Socket,\n  io: lookup,\n  connect: lookup\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default };\nexport { Fetch, NodeXHR, XHR, NodeWebSocket, WebSocket, WebTransport } from \"engine.io-client\";", "map": {"version": 3, "names": ["url", "Manager", "Socket", "cache", "lookup", "uri", "opts", "undefined", "parsed", "path", "source", "id", "sameNamespace", "newConnection", "forceNew", "multiplex", "io", "query", "query<PERSON><PERSON>", "socket", "Object", "assign", "connect", "protocol", "default", "<PERSON>tch", "NodeXHR", "XHR", "NodeWebSocket", "WebSocket", "WebTransport"], "sources": ["/app/node_modules/socket.io-client/build/esm/index.js"], "sourcesContent": ["import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\nexport { Fetch, NodeXHR, XHR, NodeWebSocket, WebSocket, WebTransport, } from \"engine.io-client\";\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,UAAU;AAC9B,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC;AACA;AACA;AACA,MAAMC,KAAK,GAAG,CAAC,CAAC;AAChB,SAASC,MAAMA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACvB,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IACzBC,IAAI,GAAGD,GAAG;IACVA,GAAG,GAAGE,SAAS;EACnB;EACAD,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EACjB,MAAME,MAAM,GAAGR,GAAG,CAACK,GAAG,EAAEC,IAAI,CAACG,IAAI,IAAI,YAAY,CAAC;EAClD,MAAMC,MAAM,GAAGF,MAAM,CAACE,MAAM;EAC5B,MAAMC,EAAE,GAAGH,MAAM,CAACG,EAAE;EACpB,MAAMF,IAAI,GAAGD,MAAM,CAACC,IAAI;EACxB,MAAMG,aAAa,GAAGT,KAAK,CAACQ,EAAE,CAAC,IAAIF,IAAI,IAAIN,KAAK,CAACQ,EAAE,CAAC,CAAC,MAAM,CAAC;EAC5D,MAAME,aAAa,GAAGP,IAAI,CAACQ,QAAQ,IAC/BR,IAAI,CAAC,sBAAsB,CAAC,IAC5B,KAAK,KAAKA,IAAI,CAACS,SAAS,IACxBH,aAAa;EACjB,IAAII,EAAE;EACN,IAAIH,aAAa,EAAE;IACfG,EAAE,GAAG,IAAIf,OAAO,CAACS,MAAM,EAAEJ,IAAI,CAAC;EAClC,CAAC,MACI;IACD,IAAI,CAACH,KAAK,CAACQ,EAAE,CAAC,EAAE;MACZR,KAAK,CAACQ,EAAE,CAAC,GAAG,IAAIV,OAAO,CAACS,MAAM,EAAEJ,IAAI,CAAC;IACzC;IACAU,EAAE,GAAGb,KAAK,CAACQ,EAAE,CAAC;EAClB;EACA,IAAIH,MAAM,CAACS,KAAK,IAAI,CAACX,IAAI,CAACW,KAAK,EAAE;IAC7BX,IAAI,CAACW,KAAK,GAAGT,MAAM,CAACU,QAAQ;EAChC;EACA,OAAOF,EAAE,CAACG,MAAM,CAACX,MAAM,CAACC,IAAI,EAAEH,IAAI,CAAC;AACvC;AACA;AACA;AACAc,MAAM,CAACC,MAAM,CAACjB,MAAM,EAAE;EAClBH,OAAO;EACPC,MAAM;EACNc,EAAE,EAAEZ,MAAM;EACVkB,OAAO,EAAElB;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASmB,QAAQ,QAAQ,kBAAkB;AAC3C;AACA;AACA;AACA;AACA;AACA,SAAStB,OAAO,EAAEC,MAAM,EAAEE,MAAM,IAAIY,EAAE,EAAEZ,MAAM,IAAIkB,OAAO,EAAElB,MAAM,IAAIoB,OAAO;AAC5E,SAASC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEC,aAAa,EAAEC,SAAS,EAAEC,YAAY,QAAS,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
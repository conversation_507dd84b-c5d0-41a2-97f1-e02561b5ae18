{"ast": null, "code": "var _jsxFileName = \"/app/src/App.jsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport LoginPage from './pages/LoginPage';\nimport Dashboard from './components/Dashboard';\nimport AdminPanel from './pages/AdminPanel';\nimport ChatPanel from './components/ChatPanel';\nimport NewIncidentForm from './components/NewIncidentForm';\nimport Sidebar from './components/Sidebar';\nimport { AuthProvider } from './context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex\",\n        children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 p-4\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 19,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/dashboard\",\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin\",\n              element: /*#__PURE__*/_jsxDEV(AdminPanel, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/chat\",\n              element: /*#__PURE__*/_jsxDEV(ChatPanel, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/incident\",\n              element: /*#__PURE__*/_jsxDEV(NewIncidentForm, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "LoginPage", "Dashboard", "AdminPanel", "ChatPanel", "NewIncidentForm", "Sidebar", "<PERSON>th<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["/app/src/App.jsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport LoginPage from './pages/LoginPage';\nimport Dashboard from './components/Dashboard';\nimport AdminPanel from './pages/AdminPanel';\nimport ChatPanel from './components/ChatPanel';\nimport NewIncidentForm from './components/NewIncidentForm';\nimport Sidebar from './components/Sidebar';\nimport { AuthProvider } from './context/AuthContext';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"flex\">\n          <Sidebar />\n          <div className=\"flex-1 p-4\">\n            <Routes>\n              <Route path=\"/\" element={<LoginPage />} />\n              <Route path=\"/dashboard\" element={<Dashboard />} />\n              <Route path=\"/admin\" element={<AdminPanel />} />\n              <Route path=\"/chat\" element={<ChatPanel />} />\n              <Route path=\"/incident\" element={<NewIncidentForm />} />\n            </Routes>\n          </div>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,SAASC,YAAY,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACF,YAAY;IAAAI,QAAA,eACXF,OAAA,CAACX,MAAM;MAAAa,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,MAAM;QAAAD,QAAA,gBACnBF,OAAA,CAACH,OAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACXP,OAAA;UAAKG,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzBF,OAAA,CAACV,MAAM;YAAAY,QAAA,gBACLF,OAAA,CAACT,KAAK;cAACiB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAET,OAAA,CAACR,SAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1CP,OAAA,CAACT,KAAK;cAACiB,IAAI,EAAC,YAAY;cAACC,OAAO,eAAET,OAAA,CAACP,SAAS;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDP,OAAA,CAACT,KAAK;cAACiB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAET,OAAA,CAACN,UAAU;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDP,OAAA,CAACT,KAAK;cAACiB,IAAI,EAAC,OAAO;cAACC,OAAO,eAAET,OAAA,CAACL,SAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9CP,OAAA,CAACT,KAAK;cAACiB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAET,OAAA,CAACJ,eAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACG,EAAA,GAnBQT,GAAG;AAqBZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
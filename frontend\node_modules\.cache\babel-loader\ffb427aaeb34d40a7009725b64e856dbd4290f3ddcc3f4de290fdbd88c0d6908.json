{"ast": null, "code": "var _jsxFileName = \"/app/src/components/NewIncidentForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction NewIncidentForm() {\n  _s();\n  const [form, setForm] = useState({\n    title: '',\n    description: '',\n    location: '',\n    priority: 'Medium',\n    category_id: 1\n  });\n  const {\n    token\n  } = useAuth();\n  const handleChange = e => setForm({\n    ...form,\n    [e.target.name]: e.target.value\n  });\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      await axios.post('http://localhost:4000/api/incidents', form, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      alert('Incident reported!');\n    } catch (err) {\n      alert('Error submitting incident');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pl-64 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-bold mb-4\",\n      children: \"Report New Incident\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"grid grid-cols-1 gap-4 max-w-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"title\",\n        className: \"p-2 border\",\n        placeholder: \"Title\",\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        name: \"description\",\n        className: \"p-2 border\",\n        placeholder: \"Description\",\n        onChange: handleChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"location\",\n        className: \"p-2 border\",\n        placeholder: \"Location\",\n        onChange: handleChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        name: \"priority\",\n        className: \"p-2 border\",\n        onChange: handleChange,\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Low\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Medium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 31\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"High\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 54\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          children: \"Critical\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 75\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"bg-green-600 text-white py-2\",\n        children: \"Submit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n}\n_s(NewIncidentForm, \"fRQSE3xLn+LLx6MinAJjnbycvjE=\", false, function () {\n  return [useAuth];\n});\n_c = NewIncidentForm;\nexport default NewIncidentForm;\nvar _c;\n$RefreshReg$(_c, \"NewIncidentForm\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useAuth", "jsxDEV", "_jsxDEV", "NewIncidentForm", "_s", "form", "setForm", "title", "description", "location", "priority", "category_id", "token", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "post", "headers", "Authorization", "alert", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "placeholder", "onChange", "required", "type", "_c", "$RefreshReg$"], "sources": ["/app/src/components/NewIncidentForm.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\nimport { useAuth } from '../context/AuthContext';\r\n\r\nfunction NewIncidentForm() {\r\n  const [form, setForm] = useState({\r\n    title: '', description: '', location: '', priority: 'Medium', category_id: 1\r\n  });\r\n  const { token } = useAuth();\r\n\r\n  const handleChange = e => setForm({ ...form, [e.target.name]: e.target.value });\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    try {\r\n      await axios.post('http://localhost:4000/api/incidents', form, {\r\n        headers: { Authorization: `Bearer ${token}` }\r\n      });\r\n      alert('Incident reported!');\r\n    } catch (err) {\r\n      alert('Error submitting incident');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"pl-64 p-6\">\r\n      <h2 className=\"text-xl font-bold mb-4\">Report New Incident</h2>\r\n      <form onSubmit={handleSubmit} className=\"grid grid-cols-1 gap-4 max-w-lg\">\r\n        <input name=\"title\" className=\"p-2 border\" placeholder=\"Title\" onChange={handleChange} required />\r\n        <textarea name=\"description\" className=\"p-2 border\" placeholder=\"Description\" onChange={handleChange} />\r\n        <input name=\"location\" className=\"p-2 border\" placeholder=\"Location\" onChange={handleChange} />\r\n        <select name=\"priority\" className=\"p-2 border\" onChange={handleChange}>\r\n          <option>Low</option><option>Medium</option><option>High</option><option>Critical</option>\r\n        </select>\r\n        <button type=\"submit\" className=\"bg-green-600 text-white py-2\">Submit</button>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default NewIncidentForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGR,QAAQ,CAAC;IAC/BS,KAAK,EAAE,EAAE;IAAEC,WAAW,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,WAAW,EAAE;EAC7E,CAAC,CAAC;EACF,MAAM;IAAEC;EAAM,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAE3B,MAAMa,YAAY,GAAGC,CAAC,IAAIR,OAAO,CAAC;IAAE,GAAGD,IAAI;IAAE,CAACS,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;EAAM,CAAC,CAAC;EAE/E,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMpB,KAAK,CAACqB,IAAI,CAAC,qCAAqC,EAAEf,IAAI,EAAE;QAC5DgB,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUV,KAAK;QAAG;MAC9C,CAAC,CAAC;MACFW,KAAK,CAAC,oBAAoB,CAAC;IAC7B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZD,KAAK,CAAC,2BAA2B,CAAC;IACpC;EACF,CAAC;EAED,oBACErB,OAAA;IAAKuB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBxB,OAAA;MAAIuB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/D5B,OAAA;MAAM6B,QAAQ,EAAEb,YAAa;MAACO,SAAS,EAAC,iCAAiC;MAAAC,QAAA,gBACvExB,OAAA;QAAOc,IAAI,EAAC,OAAO;QAACS,SAAS,EAAC,YAAY;QAACO,WAAW,EAAC,OAAO;QAACC,QAAQ,EAAEpB,YAAa;QAACqB,QAAQ;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClG5B,OAAA;QAAUc,IAAI,EAAC,aAAa;QAACS,SAAS,EAAC,YAAY;QAACO,WAAW,EAAC,aAAa;QAACC,QAAQ,EAAEpB;MAAa;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxG5B,OAAA;QAAOc,IAAI,EAAC,UAAU;QAACS,SAAS,EAAC,YAAY;QAACO,WAAW,EAAC,UAAU;QAACC,QAAQ,EAAEpB;MAAa;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/F5B,OAAA;QAAQc,IAAI,EAAC,UAAU;QAACS,SAAS,EAAC,YAAY;QAACQ,QAAQ,EAAEpB,YAAa;QAAAa,QAAA,gBACpExB,OAAA;UAAAwB,QAAA,EAAQ;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAAA5B,OAAA;UAAAwB,QAAA,EAAQ;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAAA5B,OAAA;UAAAwB,QAAA,EAAQ;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAAA5B,OAAA;UAAAwB,QAAA,EAAQ;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CAAC,eACT5B,OAAA;QAAQiC,IAAI,EAAC,QAAQ;QAACV,SAAS,EAAC,8BAA8B;QAAAC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1E,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAAC1B,EAAA,CAlCQD,eAAe;EAAA,QAIJH,OAAO;AAAA;AAAAoC,EAAA,GAJlBjC,eAAe;AAoCxB,eAAeA,eAAe;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
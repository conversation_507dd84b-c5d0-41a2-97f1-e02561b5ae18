{"name": "alphaeventwatch-frontend", "version": "1.0.0", "private": true, "dependencies": {"axios": "^1.10.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.1", "react-scripts": "^5.0.1", "socket.io-client": "^4.7.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
import React, { useEffect, useState } from 'react';
import io from 'socket.io-client';
import { useAuth } from '../context/AuthContext';

const socket = io('http://localhost:4000');

function ChatPanel() {
  const { token } = useAuth();
  const [msg, setMsg] = useState('');
  const [chat, setChat] = useState([]);

  useEffect(() => {
    socket.on('receiveMessage', message => {
      setChat(prev => [...prev, message]);
    });

    return () => socket.off('receiveMessage');
  }, []);

  const sendMessage = () => {
    socket.emit('sendMessage', msg);
    setChat(prev => [...prev, `You: ${msg}`]);
    setMsg('');
  };

  return (
    <div className="pl-64 p-6">
      <h2 className="text-xl font-bold mb-2">Team Chat</h2>
      <div className="border h-64 overflow-y-scroll mb-2 p-2 bg-gray-100">
        {chat.map((m, i) => <div key={i}>{m}</div>)}
      </div>
      <input className="border p-2 w-2/3" value={msg} onChange={e => setMsg(e.target.value)} />
      <button className="bg-blue-600 text-white p-2 ml-2" onClick={sendMessage}>Send</button>
    </div>
  );
}

export default ChatPanel;

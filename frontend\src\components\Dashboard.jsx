import React, { useEffect, useState } from 'react';
import axios from 'axios';
import Sidebar from './Sidebar';
import { useAuth } from '../context/AuthContext';

function Dashboard() {
  const [incidents, setIncidents] = useState([]);
  const { token } = useAuth();

  useEffect(() => {
    axios.get('http://localhost:4000/api/incidents', {
      headers: { Authorization: `Bearer ${token}` }
    })
    .then(res => setIncidents(res.data))
    .catch(err => console.error(err));
  }, [token]);

  return (
    <div className="pl-64 p-6">
      <h1 className="text-2xl font-bold mb-4">Incident Dashboard</h1>
      <table className="w-full table-auto border-collapse border">
        <thead>
          <tr>
            <th className="border p-2">Title</th>
            <th className="border p-2">Status</th>
            <th className="border p-2">Priority</th>
            <th className="border p-2">Location</th>
          </tr>
        </thead>
        <tbody>
          {incidents.map((inc) => (
            <tr key={inc.id}>
              <td className="border p-2">{inc.title}</td>
              <td className="border p-2">{inc.status}</td>
              <td className="border p-2">{inc.priority}</td>
              <td className="border p-2">{inc.location}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default Dashboard;

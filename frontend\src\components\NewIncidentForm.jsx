import React, { useState } from 'react';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';

function NewIncidentForm() {
  const [form, setForm] = useState({
    title: '', description: '', location: '', priority: 'Medium', category_id: 1
  });
  const { token } = useAuth();

  const handleChange = e => setForm({ ...form, [e.target.name]: e.target.value });

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await axios.post('http://localhost:4000/api/incidents', form, {
        headers: { Authorization: `Bearer ${token}` }
      });
      alert('Incident reported!');
    } catch (err) {
      alert('Error submitting incident');
    }
  };

  return (
    <div className="pl-64 p-6">
      <h2 className="text-xl font-bold mb-4">Report New Incident</h2>
      <form onSubmit={handleSubmit} className="grid grid-cols-1 gap-4 max-w-lg">
        <input name="title" className="p-2 border" placeholder="Title" onChange={handleChange} required />
        <textarea name="description" className="p-2 border" placeholder="Description" onChange={handleChange} />
        <input name="location" className="p-2 border" placeholder="Location" onChange={handleChange} />
        <select name="priority" className="p-2 border" onChange={handleChange}>
          <option>Low</option><option>Medium</option><option>High</option><option>Critical</option>
        </select>
        <button type="submit" className="bg-green-600 text-white py-2">Submit</button>
      </form>
    </div>
  );
}

export default NewIncidentForm;

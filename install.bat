@echo off
echo ===============================
echo  AlphaEventWatch Deployment
echo ===============================

REM Step 1: Install backend dependencies
echo Installing backend dependencies...
cd backend
call npm install
cd ..

REM Step 2: Install frontend dependencies
echo Installing frontend dependencies...
cd frontend
call npm install
cd ..

REM Step 3: Start Docker services
echo Launching Docker containers...
docker-compose up --build -d

echo.
echo ✅ Deployment complete!
echo Frontend: http://localhost:3000
echo Backend: http://localhost:4000
pause


docker-compose exec backend node db/seed.mjs

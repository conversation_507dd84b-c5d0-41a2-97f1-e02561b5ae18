#!/bin/bash

echo "🚀 Starting AlphaEventWatch installation..."

# Step 1: Build Docker images
echo "🔧 Building Docker containers..."
docker-compose build

# Step 2: Start containers
echo "🚀 Launching containers..."
docker-compose up -d

# Step 3: Wait and run migrations
echo "🕒 Waiting for DB to initialize..."
sleep 10

echo "📦 Running Sequelize sync (from backend)..."
docker exec alphaeventwatch-backend node index.js

echo "✅ System started successfully!"
echo "📍 Backend → http://localhost:4000"
echo "📍 Frontend → http://localhost:3000"



docker-compose exec backend node db/seed.mjs
